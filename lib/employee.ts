// lib/employee.ts

import { apiGet } from './api';
import { getAccessToken, getCompanyCountryCode } from './auth';
import { EmployeeType } from '@/types/payroll';

/**
 * Get employee types for the current company's country
 * @returns Promise with employee types array
 */
export async function getEmployeeTypesForCompany(): Promise<EmployeeType[]> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const countryCode = getCompanyCountryCode();
  if (!countryCode) {
    throw new Error('Company country code not found');
  }

  try {
    // Convert country code to lowercase as required by the API
    // const lowercaseCountryCode = countryCode.toLowerCase();

    const response = await apiGet<{
      employee_types: EmployeeType[];
      country: { code: string; name: string };
      success: boolean;
      total_count?: number;
    }>(`api/countries/rw/employee-types`, {
      headers: {
        'Authorization': `Bear<PERSON> ${token}`
      }
    });

    return response.employee_types || [];
  } catch (error) {
    console.error('Error fetching employee types:', error);
    // Return empty array if employee types are not available (for attendance-only clients)
    return [];
  }
}

/**
 * Get a specific employee type by ID
 * @param employeeTypeId The employee type ID
 * @returns Promise with employee type or null if not found
 */
export async function getEmployeeTypeById(employeeTypeId: string): Promise<EmployeeType | null> {
  const employeeTypes = await getEmployeeTypesForCompany();
  return employeeTypes.find(type => type.employee_type_id === employeeTypeId) || null;
}

/**
 * Check if employee types are available for the current company
 * @returns Promise with boolean indicating if employee types are available
 */
export async function areEmployeeTypesAvailable(): Promise<boolean> {
  try {
    const employeeTypes = await getEmployeeTypesForCompany();
    return employeeTypes.length > 0;
  } catch (error) {
    // If there's an error fetching employee types, assume they're not available
    return false;
  }
}
